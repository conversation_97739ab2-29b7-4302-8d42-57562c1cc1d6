"""
Run optimized processing specifically for GTX 1050 Ti GPUs.
This script provides extra optimizations for the GTX 1050 Ti to avoid memory issues.
"""

import os
import sys
import time
import json
import argparse
from datetime import timedelta

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__)) if '__file__' in globals() else os.getcwd()
sys.path.append(current_dir)

# Add utils directory to path for gpu_config
utils_dir = os.path.join(current_dir, '..', 'utils')
sys.path.append(utils_dir)

# Debug path information (commented out for production)
# print(f"Current directory: {current_dir}")
# print(f"Utils directory: {utils_dir}")
# print(f"Utils directory exists: {os.path.exists(utils_dir)}")
# print(f"sys.path includes: {[p for p in sys.path if 'utils' in p]}")

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def run_gtx1050ti_processing(input_file, title, description, mode="hybrid"):
    """
    Run processing with GTX 1050 Ti optimizations.

    Args:
        input_file (str): Path to input file
        title (str): Title of the meeting
        description (str): Description or attendance information
        mode (str): Processing mode ('gpu', 'cpu', or 'hybrid')

    Returns:
        dict: Processing results
    """
    print("Starting GTX 1050 Ti optimized processing...")
    print(f"Input file: {input_file}")
    print(f"Mode: {mode}")

    # Import GPU configuration
    print("Importing GPU configuration...")
    from gpu_config import setup_environment, clear_memory

    # Apply GTX 1050 Ti specific optimizations
    print("Applying GTX 1050 Ti specific optimizations...")

    # Set environment variables for PyTorch memory management
    os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
    print("Set PyTorch memory management environment variables")

    # Check if GPU is available
    import torch
    if torch.cuda.is_available():
        print(f"GPU available: {torch.cuda.get_device_name(0)}")
        print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.2f} GB")
    else:
        print("No GPU available")

    # Clear GPU memory before starting
    if mode != "cpu" and torch.cuda.is_available():
        print("Clearing GPU memory...")
        clear_memory(aggressive=True)

    # Override global settings for GTX 1050 Ti
    print("Overriding global settings for GTX 1050 Ti...")
    import gpu_config
    gpu_config.GPU_MEMORY_FRACTION = 0.4  # Use only 40% of GPU memory
    gpu_config.GPU_MEMORY_THRESHOLD = 0.3  # Switch to CPU at 30% usage
    gpu_config.GTX_1050TI_MODE = True  # Enable GTX 1050 Ti optimizations
    print(f"Set GPU_MEMORY_FRACTION to {gpu_config.GPU_MEMORY_FRACTION}")
    print(f"Set GPU_MEMORY_THRESHOLD to {gpu_config.GPU_MEMORY_THRESHOLD}")
    print(f"Set GTX_1050TI_MODE to {gpu_config.GTX_1050TI_MODE}")

    # Set up environment based on mode
    print("Setting up environment...")
    if mode == "gpu":
        print("Using GPU-only mode with GTX 1050 Ti optimizations")
        gpu_config.CONSISTENT_DEVICE = True  # Stick with GPU
        print("Set CONSISTENT_DEVICE to True")
        setup_environment(force_cpu=False, force_gpu=True)
    elif mode == "cpu":
        print("Using CPU-only mode")
        setup_environment(force_cpu=True)
    else:  # hybrid mode
        print("Using hybrid mode with GTX 1050 Ti optimizations")
        gpu_config.CONSISTENT_DEVICE = False  # Allow switching between GPU and CPU
        print("Set CONSISTENT_DEVICE to False")
        setup_environment(force_cpu=False, force_gpu=False)

    # Import processing module
    print("Importing processing module...")
    try:
        from fast_process import process_file
        print("Successfully imported process_file")
    except Exception as e:
        print(f"Error importing process_file: {e}")
        raise

    # Process the file
    print(f"Starting processing of {input_file}...")
    print(f"Title: {title}")
    print(f"Description/Attendance: {description}")

    # Extract user_id from description if it's JSON
    user_id = "user"
    try:
        # Check if description is JSON and contains userId
        import json
        desc_json = json.loads(description)
        if isinstance(desc_json, list) and len(desc_json) > 0:
            # It's likely faculty attendance data
            user_id = "user"  # Default if not found
        elif isinstance(desc_json, dict) and "userId" in desc_json:
            user_id = desc_json["userId"]
    except:
        # Not JSON or doesn't contain userId
        pass

    start_time = time.time()
    try:
        result = process_file(input_file, title, description, user_id)
        print("Processing completed successfully")
    except Exception as e:
        print(f"Error during processing: {e}")
        raise
    processing_time = time.time() - start_time

    # Add processing mode to result
    print(f"Processing completed in {format_time(processing_time)}")
    result["processing_mode"] = mode
    result["processing_time"] = processing_time
    result["processing_time_formatted"] = format_time(processing_time)

    return result

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run optimized processing for GTX 1050 Ti")
    parser.add_argument("input_file", help="Path to input audio/video file")
    parser.add_argument("--title", default="Meeting", help="Title of the meeting")
    parser.add_argument("--description", default="", help="Description or attendance information")
    parser.add_argument("--mode", choices=["gpu", "cpu", "hybrid"], default="hybrid",
                        help="Processing mode: gpu (GPU only), cpu (CPU only), or hybrid (use both)")

    args = parser.parse_args()

    # Verify file exists
    if not os.path.exists(args.input_file):
        print(f"Error: File not found: {args.input_file}")
        return 1

    # Run processing
    result = run_gtx1050ti_processing(args.input_file, args.title, args.description, args.mode)

    # First print human-readable results
    print("\n=== Processing Results ===\n")
    print(f"File: {args.input_file}")
    print(f"Mode: {result['processing_mode']}")
    print(f"Status: {result['status']}")

    if result['status'] == "success":
        print(f"Processing time: {result['processing_time_formatted']} ({result['processing_time']:.2f} seconds)")
        print(f"Transcript file: {result['data']['transcript_file']}")
        print(f"Minutes file: {result['data']['minutes_file']}")
        print(f"Speakers detected: {len(result['data']['speakers'])}")
    else:
        print(f"Error: {result.get('error', 'Unknown error')}")

    # Then print JSON output with clear markers for the server to parse
    # Import the sanitize_json_data function if available
    try:
        from fast_process import sanitize_json_data
        sanitized_result = sanitize_json_data(result)
    except ImportError:
        # If not available, create a simple version
        def sanitize_simple(data):
            if isinstance(data, dict):
                return {k: sanitize_simple(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [sanitize_simple(item) for item in data]
            elif isinstance(data, str):
                return data.encode('ascii', 'replace').decode('ascii')
            else:
                return data
        sanitized_result = sanitize_simple(result)

    # Output the JSON with ensure_ascii=True to avoid encoding issues
    json_output = json.dumps(sanitized_result, ensure_ascii=True)

    # Print to both stdout and stderr with clear markers
    print("\n===JSON_OUTPUT_START===", flush=True)
    print(json_output, flush=True)
    print("===JSON_OUTPUT_END===", flush=True)

    # Also print to stderr for redundancy
    print("\n===JSON_OUTPUT_START===", file=sys.stderr, flush=True)
    print(json_output, file=sys.stderr, flush=True)
    print("===JSON_OUTPUT_END===", file=sys.stderr, flush=True)

    return 0

if __name__ == "__main__":
    sys.exit(main())
